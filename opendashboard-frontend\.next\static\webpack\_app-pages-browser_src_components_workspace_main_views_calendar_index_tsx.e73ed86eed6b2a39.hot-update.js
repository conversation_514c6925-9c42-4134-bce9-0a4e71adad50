"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, onDragStart, canEditData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-2 right-2 px-3 py-2 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { hour, date, children, onDragOver, onDrop, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        className: \"flex-1 relative border-b border-gray-100 min-h-[60px] cursor-pointer\",\n        style: {\n            height: \"60px\"\n        },\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), selectedDate));\n    const currentTimePosition = (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? {\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a simple, visible drag image\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"700px\";\n        dragImg.style.height = \"50px\";\n        dragImg.style.background = \"#1e293b\";\n        dragImg.style.border = \"1px solid #475569\";\n        dragImg.style.borderRadius = \"6px\";\n        dragImg.style.padding = \"8px 12px\";\n        dragImg.style.boxShadow = \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\";\n        dragImg.style.fontSize = \"12px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"white\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.opacity = \"0.95\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; margin-bottom: 4px; color: white; line-height: 1.2;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 10px; color: #cbd5e1; line-height: 1.2;\">\\n        ').concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        e.dataTransfer.setDragImage(dragImg, 125, 25);\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (hour, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the time slot\n        const timeSlotHeight = 60; // height of time slot in pixels\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n        const newDate = new Date(selectedDate);\n        newDate.setHours(hour, minutes, 0, 0);\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    if (dayEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events scheduled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"No events planned for \".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \".\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    const newDate = new Date(selectedDate);\n                                    newDate.setHours(new Date().getHours(), 0, 0, 0);\n                                    openAddEventForm(newDate);\n                                },\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto relative bg-white\",\n                id: \"day-view-container\",\n                children: [\n                    hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                            style: {\n                                height: \"60px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 sticky left-0 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), isMobile ? \"h\" : \"h\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[10px] text-gray-400\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), \"a\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                    hour: hour,\n                                    date: selectedDate,\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(hour, e),\n                                    onClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(selectedDate);\n                                            newDate.setHours(hour, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: dayEvents.filter((event)=>{\n                                        const eventHour = new Date(event.start).getHours();\n                                        return eventHour === hour;\n                                    }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                            event: event,\n                                            selectedEvent: selectedEvent,\n                                            style: {\n                                                top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                height: \"\".concat(Math.max(30, (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventDurationInMinutes)(event) / 60 * 100), \"%\"),\n                                                zIndex: selectedEvent === event.id ? 20 : 10\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                const container = document.getElementById(\"day-view-container\");\n                                                if (container) {\n                                                    savedScrollTop.current = container.scrollTop;\n                                                }\n                                                setSelectedEvent(event.id);\n                                                handleEventClick(event);\n                                            },\n                                            onDragStart: handleDragStart,\n                                            canEditData: canEditData\n                                        }, event.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-0 flex items-center z-30 pointer-events-none\", isMobile ? \"left-14\" : \"left-20\"),\n                        style: {\n                            top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize\n    ];\n});\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, selectedEvent, onClick, onDragStart, canEditData } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md cursor-pointer select-none shadow-sm border transition-all duration-200\", isMobile ? \"px-2 py-1 text-[10px] mb-0.5\" : \"px-2 py-1 text-xs mb-1\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-md ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700 hover:shadow-md\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-full flex-shrink-0\", isMobile ? \"w-1.5 h-1.5\" : \"w-2 h-2\", selectedEvent === event.id ? \"bg-primary-foreground/60\" : \"bg-blue-400\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"truncate font-medium leading-tight\",\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-[10px] mt-0.5 opacity-75 ml-3\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), isMobile ? \"h:mm\" : \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventItem, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c = CalendarEventItem;\nconst DayCell = (param)=>{\n    let { date, children, onClick, onDragOver, onDrop, isCurrentMonth } = param;\n    _s1();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r relative cursor-pointer transition-colors group\", isMobile ? \"min-h-[80px] p-2\" : \"min-h-[110px] p-3\", isCurrentMonth ? \"bg-card hover:bg-accent\" : \"bg-muted hover:bg-muted/80\", \"border-border\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c1 = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, onEventDrop } = param;\n    _s2();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a compact drag image for month view\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"65px\";\n        dragImg.style.height = \"33px\";\n        dragImg.style.background = \"#1e293b\"; // Dark background\n        dragImg.style.border = \"1px solid #475569\";\n        dragImg.style.borderRadius = \"4px\";\n        dragImg.style.padding = \"6px 4px\";\n        dragImg.style.boxShadow = \"0 2px 4px -1px rgb(0 0 0 / 0.15)\";\n        dragImg.style.fontSize = \"9px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"white\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.alignItems = \"center\";\n        dragImg.style.opacity = \"0.9\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.style.textAlign = \"center\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; color: white; line-height: 1.1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 62px; margin-bottom: 2px;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 7px; color: #cbd5e1; line-height: 1;\">\\n        ').concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        e.dataTransfer.setDragImage(dragImg, 40, 25);\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (date, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the cell\n        const cellHeight = e.target.offsetHeight;\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const hourPercentage = relativeY / cellHeight;\n        const hour = Math.floor(hourPercentage * 24);\n        const minutes = Math.floor((hourPercentage * 24 - hour) * 60);\n        const newDate = new Date(date);\n        newDate.setHours(hour, minutes, 0, 0);\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate);\n    const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate);\n    const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(monthStart, {\n        weekStartsOn: 0\n    });\n    const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(monthEnd, {\n        weekStartsOn: 0\n    });\n    const days = [];\n    let day = startDay;\n    while(day <= endDay){\n        days.push(day);\n        day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, 1);\n    }\n    const weeks = [];\n    for(let i = 0; i < days.length; i += 7){\n        weeks.push(days.slice(i, i + 7));\n    }\n    const monthEvents = events.filter((event)=>{\n        const eventStart = new Date(event.start);\n        return eventStart >= startDay && eventStart <= endDay;\n    });\n    if (monthEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b bg-secondary\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                            children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events this month\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: [\n                                    (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, \"MMMM yyyy\"),\n                                    \" is completely free. Start planning your month!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>openAddEventForm(selectedDate),\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 222,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full bg-background\", isMobile ? \"flex flex-col\" : \"flex\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b sticky top-0 z-10 bg-secondary\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                                children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 grid grid-cols-7 auto-rows-fr border-gray-100 border-b\",\n                        children: weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), day));\n                                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                    date: day,\n                                    isCurrentMonth: isCurrentMonth,\n                                    onClick: ()=>setSelectedDate(day),\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(day, e),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold\", isMobile ? \"text-sm w-6 h-6\" : \"text-base w-7 h-7\", isCurrentDay ? \"bg-primary text-primary-foreground shadow-sm\" : isCurrentMonth ? \"text-foreground hover:bg-accent\" : \"text-muted-foreground\"),\n                                                    children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(day, \"d\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canEditData && isCurrentMonth && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openAddEventForm(day);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-1\", isMobile && \"space-y-0.5\"),\n                                            children: [\n                                                dayEvents.slice(0, isMobile ? 2 : 4).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                                        event: event,\n                                                        selectedEvent: selectedEvent,\n                                                        canEditData: canEditData,\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            setSelectedEvent(event.id);\n                                                            handleEventClick(event);\n                                                        },\n                                                        onDragStart: handleDragStart\n                                                    }, event.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                dayEvents.length > (isMobile ? 2 : 4) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-primary hover:text-primary/80 font-medium pl-3 py-1 rounded-md hover:bg-accent transition-colors\", isMobile ? \"text-[10px]\" : \"text-xs\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedDate(day);\n                                                    },\n                                                    children: [\n                                                        \"+ \",\n                                                        dayEvents.length - (isMobile ? 2 : 4),\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(weekIndex, \"-\").concat(dayIndex), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 17\n                                }, undefined);\n                            }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, undefined),\n            (!isMobile || isMobile && selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-l border-gray-200 flex flex-col bg-gray-50\", isMobile ? \"w-full border-t\" : \"w-96\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-gray-200 bg-white\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-bold text-gray-900 mb-1\", isMobile ? \"text-lg\" : \"text-xl\"),\n                                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, isMobile ? \"MMM d, yyyy\" : \"MMMM d, yyyy\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length,\n                                    \" events scheduled\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).sort((a, b)=>new Date(a.start).getTime() - new Date(b.start).getTime()).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"cursor-pointer transition-all duration-200 border-0 shadow-sm\", isMobile ? \"p-3\" : \"p-4\", selectedEvent === event.id ? \"bg-primary text-primary-foreground shadow-lg ring-2 ring-primary/20\" : \"bg-primary/5 hover:shadow-md hover:bg-primary/10\"),\n                                        onClick: ()=>{\n                                            setSelectedEvent(event.id);\n                                            handleEventClick(event);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold mb-2\", isMobile ? \"text-sm\" : \"text-base\", selectedEvent === event.id ? \"text-primary-foreground\" : \"text-foreground\"),\n                                                children: event.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", isMobile ? \"text-xs\" : \"text-sm\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                            lineNumber: 426,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"),\n                                                            \" - \",\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.end), \"h:mm a\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 428,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, event.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, undefined),\n                            events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-primary\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground mb-4\", isMobile ? \"text-sm\" : \"text-base\"),\n                                        children: \"No events on this day\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>openAddEventForm(selectedDate),\n                                        className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 rounded-lg shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Add Event\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 371,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c2 = MonthView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"DayCell\");\n$RefreshReg$(_c2, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});