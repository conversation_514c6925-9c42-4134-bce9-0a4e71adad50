"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx":
/*!*******************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/MonthView.tsx ***!
  \*******************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MonthView: function() { return /* binding */ MonthView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfMonth/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/startOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/endOfWeek/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/addDays/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=addDays,endOfMonth,endOfWeek,format,isSameDay,isToday,startOfMonth,startOfWeek!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, selectedEvent, onClick, onDragStart, canEditData } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-md cursor-pointer select-none shadow-sm border transition-all duration-200\", isMobile ? \"px-2 py-1 text-[10px] mb-0.5\" : \"px-2 py-1 text-xs mb-1\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-md ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700 hover:shadow-md\"),\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-full flex-shrink-0\", isMobile ? \"w-1.5 h-1.5\" : \"w-2 h-2\", selectedEvent === event.id ? \"bg-primary-foreground/60\" : \"bg-blue-400\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"truncate font-medium leading-tight\",\n                        children: event.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-[10px] mt-0.5 opacity-75 ml-3\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), isMobile ? \"h:mm\" : \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CalendarEventItem, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c = CalendarEventItem;\nconst DayCell = (param)=>{\n    let { date, children, onClick, onDragOver, onDrop, isCurrentMonth } = param;\n    _s1();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        onClick: onClick,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-r relative cursor-pointer transition-colors group\", isMobile ? \"min-h-[80px] p-2\" : \"min-h-[110px] p-3\", isCurrentMonth ? \"bg-card hover:bg-accent\" : \"bg-muted hover:bg-muted/80\", \"border-border\"),\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(DayCell, \"NhNlQCKT7mqGuQWPmw42Yz4hgLE=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c1 = DayCell;\nconst MonthView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, setSelectedDate, openAddEventForm, canEditData, handleEventClick, onEventDrop } = param;\n    _s2();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a compact drag image for month view\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"80px\";\n        dragImg.style.height = \"40px\";\n        dragImg.style.background = \"#1e293b\"; // Dark background\n        dragImg.style.border = \"1px solid #475569\";\n        dragImg.style.borderRadius = \"4px\";\n        dragImg.style.padding = \"6px 4px\";\n        dragImg.style.boxShadow = \"0 2px 4px -1px rgb(0 0 0 / 0.15)\";\n        dragImg.style.fontSize = \"9px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"white\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.alignItems = \"center\";\n        dragImg.style.opacity = \"0.9\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.style.textAlign = \"center\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; color: white; line-height: 1.1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 62px; margin-bottom: 2px;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 7px; color: #cbd5e1; line-height: 1;\">\\n        ').concat((0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        // Set drag image with centered offset for compact appearance\n        e.dataTransfer.setDragImage(dragImg, 30, 20);\n        // Clean up after a short delay\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (date, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the cell\n        const cellHeight = e.target.offsetHeight;\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const hourPercentage = relativeY / cellHeight;\n        const hour = Math.floor(hourPercentage * 24);\n        const minutes = Math.floor((hourPercentage * 24 - hour) * 60);\n        // Create new date with calculated time\n        const newDate = new Date(date);\n        newDate.setHours(hour, minutes, 0, 0);\n        // Check if the new date is the same as the original\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    const monthStart = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate);\n    const monthEnd = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(selectedDate);\n    const startDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(monthStart, {\n        weekStartsOn: 0\n    });\n    const endDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(monthEnd, {\n        weekStartsOn: 0\n    });\n    const days = [];\n    let day = startDay;\n    while(day <= endDay){\n        days.push(day);\n        day = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(day, 1);\n    }\n    const weeks = [];\n    for(let i = 0; i < days.length; i += 7){\n        weeks.push(days.slice(i, i + 7));\n    }\n    const monthEvents = events.filter((event)=>{\n        const eventStart = new Date(event.start);\n        return eventStart >= startDay && eventStart <= endDay;\n    });\n    if (monthEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-7 border-b bg-secondary\",\n                    children: [\n                        \"Sunday\",\n                        \"Monday\",\n                        \"Tuesday\",\n                        \"Wednesday\",\n                        \"Thursday\",\n                        \"Friday\",\n                        \"Saturday\"\n                    ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                            children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                        }, dayName, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 230,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events this month\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: [\n                                    (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, \"MMMM yyyy\"),\n                                    \" is completely free. Start planning your month!\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>openAddEventForm(selectedDate),\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 4v16m8-8H4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n            lineNumber: 228,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"h-full bg-background\", isMobile ? \"flex flex-col\" : \"flex\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col min-h-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-7 border-b sticky top-0 z-10 bg-secondary\",\n                        children: [\n                            \"Sunday\",\n                            \"Monday\",\n                            \"Tuesday\",\n                            \"Wednesday\",\n                            \"Thursday\",\n                            \"Friday\",\n                            \"Saturday\"\n                        ].map((dayName)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-center font-semibold text-foreground\", isMobile ? \"py-3 text-xs\" : \"py-4 text-sm\"),\n                                children: isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)\n                            }, dayName, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 grid grid-cols-7 auto-rows-fr border-gray-100 border-b\",\n                        children: weeks.map((week, weekIndex)=>week.map((day, dayIndex)=>{\n                                const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), day));\n                                const isCurrentMonth = day.getMonth() === selectedDate.getMonth();\n                                const isCurrentDay = (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(day);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DayCell, {\n                                    date: day,\n                                    isCurrentMonth: isCurrentMonth,\n                                    onClick: ()=>setSelectedDate(day),\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(day, e),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center rounded-full font-semibold\", isMobile ? \"text-sm w-6 h-6\" : \"text-base w-7 h-7\", isCurrentDay ? \"bg-primary text-primary-foreground shadow-sm\" : isCurrentMonth ? \"text-foreground hover:bg-accent\" : \"text-muted-foreground\"),\n                                                    children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(day, \"d\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                canEditData && isCurrentMonth && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    className: \"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent\",\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        openAddEventForm(day);\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-3 w-3 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"space-y-1\", isMobile && \"space-y-0.5\"),\n                                            children: [\n                                                dayEvents.slice(0, isMobile ? 2 : 4).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                                        event: event,\n                                                        selectedEvent: selectedEvent,\n                                                        canEditData: canEditData,\n                                                        onClick: (e)=>{\n                                                            e.stopPropagation();\n                                                            setSelectedEvent(event.id);\n                                                            handleEventClick(event);\n                                                        },\n                                                        onDragStart: handleDragStart\n                                                    }, event.id, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 23\n                                                    }, undefined)),\n                                                dayEvents.length > (isMobile ? 2 : 4) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-primary hover:text-primary/80 font-medium pl-3 py-1 rounded-md hover:bg-accent transition-colors\", isMobile ? \"text-[10px]\" : \"text-xs\"),\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        setSelectedDate(day);\n                                                    },\n                                                    children: [\n                                                        \"+ \",\n                                                        dayEvents.length - (isMobile ? 2 : 4),\n                                                        \" more\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 354,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, \"\".concat(weekIndex, \"-\").concat(dayIndex), true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, undefined);\n                            }))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined),\n            (!isMobile || isMobile && selectedEvent) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-l border-gray-200 flex flex-col bg-gray-50\", isMobile ? \"w-full border-t\" : \"w-96\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"border-b border-gray-200 bg-white\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-bold text-gray-900 mb-1\", isMobile ? \"text-lg\" : \"text-xl\"),\n                                children: (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(selectedDate, isMobile ? \"MMM d, yyyy\" : \"MMMM d, yyyy\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length,\n                                    \" events scheduled\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_5__.ScrollArea, {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex-1\", isMobile ? \"p-4\" : \"p-6\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).sort((a, b)=>new Date(a.start).getTime() - new Date(b.start).getTime()).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"cursor-pointer transition-all duration-200 border-0 shadow-sm\", isMobile ? \"p-3\" : \"p-4\", selectedEvent === event.id ? \"bg-primary text-primary-foreground shadow-lg ring-2 ring-primary/20\" : \"bg-primary/5 hover:shadow-md hover:bg-primary/10\"),\n                                        onClick: ()=>{\n                                            setSelectedEvent(event.id);\n                                            handleEventClick(event);\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"font-semibold mb-2\", isMobile ? \"text-sm\" : \"text-base\", selectedEvent === event.id ? \"text-primary-foreground\" : \"text-foreground\"),\n                                                children: event.title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center space-x-2\", isMobile ? \"text-xs\" : \"text-sm\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), \"h:mm a\"),\n                                                            \" - \",\n                                                            (0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.end), \"h:mm a\")\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, event.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 13\n                            }, undefined),\n                            events.filter((event)=>(0,_barrel_optimize_names_addDays_endOfMonth_endOfWeek_format_isSameDay_isToday_startOfMonth_startOfWeek_date_fns__WEBPACK_IMPORTED_MODULE_13__[\"default\"])(new Date(event.start), selectedDate)).length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-primary\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground mb-4\", isMobile ? \"text-sm\" : \"text-base\"),\n                                        children: \"No events on this day\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>openAddEventForm(selectedDate),\n                                        className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 rounded-lg shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 mr-2\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M12 4v16m8-8H4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Add Event\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n                lineNumber: 377,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\MonthView.tsx\",\n        lineNumber: 273,\n        columnNumber: 5\n    }, undefined);\n};\n_s2(MonthView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_6__.useScreenSize\n    ];\n});\n_c2 = MonthView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"DayCell\");\n$RefreshReg$(_c2, \"MonthView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/MonthView.tsx\n"));

/***/ })

});