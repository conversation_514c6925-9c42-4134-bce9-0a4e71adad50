"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_calendar_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx":
/*!*****************************************************************************!*\
  !*** ./src/components/workspace/main/views/calendar/components/DayView.tsx ***!
  \*****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DayView: function() { return /* binding */ DayView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isSameDay/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/isToday/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format,isSameDay,isToday,setHours!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/setHours/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/dateUtils */ \"(app-pages-browser)/./src/utils/dateUtils.ts\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=PlusIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst CalendarEventItem = (param)=>{\n    let { event, style, selectedEvent, onClick, onDragStart, canEditData } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: canEditData,\n        onDragStart: (e)=>onDragStart(event, e),\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute left-2 right-2 px-3 py-2 rounded-md text-xs shadow-sm border cursor-pointer\", \"transition-all duration-200 hover:shadow-md\", selectedEvent === event.id ? \"bg-primary text-primary-foreground border-primary shadow-lg ring-2 ring-primary/20\" : \"bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700\"),\n        style: style,\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium truncate leading-tight\",\n                children: event.title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-[10px] mt-0.5 opacity-75\", selectedEvent === event.id ? \"text-primary-foreground/80\" : \"text-muted-foreground\"),\n                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = CalendarEventItem;\nconst TimeSlot = (param)=>{\n    let { hour, date, children, onDragOver, onDrop, onClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        onDragOver: onDragOver,\n        onDrop: onDrop,\n        className: \"flex-1 relative border-b border-gray-100 min-h-[60px] cursor-pointer\",\n        style: {\n            height: \"60px\"\n        },\n        onClick: onClick,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = TimeSlot;\nconst DayView = (param)=>{\n    let { selectedDate, events, selectedEvent, setSelectedEvent, openAddEventForm, canEditData, savedScrollTop, handleEventClick, onEventDrop } = param;\n    _s();\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize)();\n    const draggedEvent = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const hours = Array.from({\n        length: 24\n    }, (_, i)=>i);\n    const dayEvents = events.filter((event)=>(0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(new Date(event.start), selectedDate));\n    const currentTimePosition = (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? {\n        hour: new Date().getHours(),\n        minutes: new Date().getMinutes()\n    } : null;\n    const handleDragStart = (event, e)=>{\n        if (!canEditData) return;\n        draggedEvent.current = event;\n        // Create a simple, visible drag image\n        const dragImg = document.createElement(\"div\");\n        dragImg.style.position = \"absolute\";\n        dragImg.style.top = \"-1000px\";\n        dragImg.style.left = \"0px\";\n        dragImg.style.width = \"2000px\";\n        dragImg.style.height = \"50px\";\n        dragImg.style.background = \"#1e293b\";\n        dragImg.style.border = \"1px solid #475569\";\n        dragImg.style.borderRadius = \"6px\";\n        dragImg.style.padding = \"8px 12px\";\n        dragImg.style.boxShadow = \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\";\n        dragImg.style.fontSize = \"12px\";\n        dragImg.style.fontWeight = \"500\";\n        dragImg.style.color = \"white\";\n        dragImg.style.display = \"flex\";\n        dragImg.style.flexDirection = \"column\";\n        dragImg.style.justifyContent = \"center\";\n        dragImg.style.opacity = \"0.95\";\n        dragImg.style.zIndex = \"9999\";\n        dragImg.innerHTML = '\\n      <div style=\"font-weight: 600; margin-bottom: 4px; color: white; line-height: 1.2;\">\\n        '.concat(event.title, '\\n      </div>\\n      <div style=\"font-size: 10px; color: #cbd5e1; line-height: 1.2;\">\\n        ').concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(new Date(event.start), \"h:mm a\"), \"\\n      </div>\\n    \");\n        document.body.appendChild(dragImg);\n        e.dataTransfer.setDragImage(dragImg, 125, 25);\n        setTimeout(()=>{\n            if (document.body.contains(dragImg)) {\n                document.body.removeChild(dragImg);\n            }\n        }, 100);\n    };\n    const handleDragOver = (e)=>{\n        if (!canEditData) return;\n        e.preventDefault();\n        e.dataTransfer.dropEffect = \"move\";\n    };\n    const handleDrop = (hour, e)=>{\n        e.preventDefault();\n        if (!canEditData || !draggedEvent.current) return;\n        const event = draggedEvent.current;\n        const originalDate = new Date(event.start);\n        // Calculate minutes based on drop position within the time slot\n        const timeSlotHeight = 60; // height of time slot in pixels\n        const rect = e.target.getBoundingClientRect();\n        const relativeY = e.clientY - rect.top;\n        const minutes = Math.floor(relativeY / timeSlotHeight * 60);\n        const newDate = new Date(selectedDate);\n        newDate.setHours(hour, minutes, 0, 0);\n        if (newDate.getTime() === originalDate.getTime()) {\n            draggedEvent.current = null;\n            return;\n        }\n        onEventDrop === null || onEventDrop === void 0 ? void 0 : onEventDrop(event, newDate);\n        draggedEvent.current = null;\n    };\n    if (dayEvents.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-b bg-secondary sticky top-0 z-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 192,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center max-w-md mx-auto px-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-primary\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground mb-2\",\n                                children: \"No events scheduled\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground mb-6\",\n                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"You have a free day ahead! Add an event to get started.\" : \"No events planned for \".concat((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE, MMMM d\"), \".\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 13\n                            }, undefined),\n                            canEditData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: ()=>{\n                                    const newDate = new Date(selectedDate);\n                                    newDate.setHours(new Date().getHours(), 0, 0, 0);\n                                    openAddEventForm(newDate);\n                                },\n                                className: \"bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PlusIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"Create Event\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n            lineNumber: 190,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-b bg-secondary sticky top-0 z-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-center\", isMobile ? \"py-3\" : \"py-4\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"font-semibold text-foreground mb-1\", isMobile ? \"text-sm\" : \"text-base\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"EEEE\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex items-center justify-center font-medium\", isMobile ? \"text-base w-6 h-6\" : \"text-lg w-8 h-8\", (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(selectedDate) ? \"bg-primary text-primary-foreground rounded-full shadow-sm\" : \"text-muted-foreground\"),\n                            children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(selectedDate, \"d\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                    lineNumber: 255,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto relative bg-white\",\n                id: \"day-view-container\",\n                children: [\n                    hours.map((hour, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex border-b border-gray-50 hover:bg-gray-25 transition-colors\",\n                            style: {\n                                height: \"60px\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex items-start justify-end pr-4 pt-2 text-xs font-medium text-gray-600 border-r border-gray-200 sticky left-0 bg-white z-10\", isMobile ? \"w-14\" : \"w-20\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-semibold\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), isMobile ? \"h\" : \"h\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-[10px] text-gray-400\",\n                                                children: (0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((0,_barrel_optimize_names_format_isSameDay_isToday_setHours_date_fns__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(selectedDate, hour), \"a\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TimeSlot, {\n                                    hour: hour,\n                                    date: selectedDate,\n                                    onDragOver: handleDragOver,\n                                    onDrop: (e)=>handleDrop(hour, e),\n                                    onClick: ()=>{\n                                        if (canEditData) {\n                                            const newDate = new Date(selectedDate);\n                                            newDate.setHours(hour, 0, 0, 0);\n                                            openAddEventForm(newDate);\n                                        }\n                                    },\n                                    children: dayEvents.filter((event)=>{\n                                        const eventHour = new Date(event.start).getHours();\n                                        return eventHour === hour;\n                                    }).map((event)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CalendarEventItem, {\n                                            event: event,\n                                            selectedEvent: selectedEvent,\n                                            style: {\n                                                top: \"\".concat(new Date(event.start).getMinutes() / 60 * 100, \"%\"),\n                                                height: \"\".concat(Math.max(30, (0,_utils_dateUtils__WEBPACK_IMPORTED_MODULE_4__.getEventDurationInMinutes)(event) / 60 * 100), \"%\"),\n                                                zIndex: selectedEvent === event.id ? 20 : 10\n                                            },\n                                            onClick: (e)=>{\n                                                e.stopPropagation();\n                                                const container = document.getElementById(\"day-view-container\");\n                                                if (container) {\n                                                    savedScrollTop.current = container.scrollTop;\n                                                }\n                                                setSelectedEvent(event.id);\n                                                handleEventClick(event);\n                                            },\n                                            onDragStart: handleDragStart,\n                                            canEditData: canEditData\n                                        }, event.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)),\n                    currentTimePosition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-0 flex items-center z-30 pointer-events-none\", isMobile ? \"left-14\" : \"left-20\"),\n                        style: {\n                            top: \"\".concat((currentTimePosition.hour + currentTimePosition.minutes / 60) * 60, \"px\")\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-3 h-3 rounded-full bg-red-500 border-2 border-white shadow-lg -ml-1.5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 border-t-2 border-red-500 shadow-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\mic\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\calendar\\\\components\\\\DayView.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DayView, \"i4bDb/LjeME9N8W2ex78n/o1OD0=\", false, function() {\n    return [\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_5__.useScreenSize\n    ];\n});\n_c2 = DayView;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"CalendarEventItem\");\n$RefreshReg$(_c1, \"TimeSlot\");\n$RefreshReg$(_c2, \"DayView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/calendar/components/DayView.tsx\n"));

/***/ })

});