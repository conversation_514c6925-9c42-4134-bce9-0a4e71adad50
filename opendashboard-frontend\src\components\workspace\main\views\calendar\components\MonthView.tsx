import React, { useRef } from 'react';
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameDay, isToday } from 'date-fns';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { PlusIcon } from '@heroicons/react/24/outline';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { CalendarEvent } from '../index';
import { useScreenSize } from '@/providers/screenSize';

interface MonthViewProps {
  selectedDate: Date;
  events: CalendarEvent[];
  selectedEvent: string | null;
  setSelectedEvent: (id: string) => void;
  setSelectedDate: (date: Date) => void;
  openAddEventForm: (date: Date) => void;
  canEditData: boolean;
  handleEventClick: (event: CalendarEvent) => void;
  onEventDrop?: (event: CalendarEvent, newDate: Date) => void;
}

const CalendarEventItem = ({
  event,
  selectedEvent,
  onClick,
  onDragStart,
  canEditData
}: {
  event: CalendarEvent;
  selectedEvent: string | null;
  onClick: (e: React.MouseEvent) => void;
  onDragStart: (event: CalendarEvent, e: React.DragEvent) => void;
  canEditData: boolean;
}) => {
  const { isMobile } = useScreenSize();
  return (
    <div
      draggable={canEditData}
      onDragStart={(e) => onDragStart(event, e)}
      className={cn(
        "rounded-md cursor-pointer select-none shadow-sm border transition-all duration-200",
        isMobile ? "px-2 py-1 text-[10px] mb-0.5" : "px-2 py-1 text-xs mb-1",
        selectedEvent === event.id
          ? "bg-primary text-primary-foreground border-primary shadow-md ring-2 ring-primary/20"
          : "bg-slate-800 text-white border-slate-700 hover:border-primary/30 hover:bg-slate-700 hover:shadow-md"
      )}
      onClick={onClick}
    >
      <div className="flex items-center space-x-1.5">
        <span className={cn(
          "rounded-full flex-shrink-0",
          isMobile ? "w-1.5 h-1.5" : "w-2 h-2",
          selectedEvent === event.id ? "bg-primary-foreground/60" : "bg-blue-400"
        )} />
        <span className="truncate font-medium leading-tight">
          {event.title}
        </span>
      </div>
      <div className={cn(
        "text-[10px] mt-0.5 opacity-75 ml-3",
        selectedEvent === event.id ? "text-primary-foreground/80" : "text-muted-foreground"
      )}>
        {format(new Date(event.start), isMobile ? 'h:mm' : 'h:mm a')}
      </div>
    </div>
  );
};

const DayCell = ({
  date,
  children,
  onClick,
  onDragOver,
  onDrop,
  isCurrentMonth
}: {
  date: Date;
  children: React.ReactNode;
  onClick: () => void;
  onDragOver: (e: React.DragEvent) => void;
  onDrop: (e: React.DragEvent) => void;
  isCurrentMonth: boolean;
}) => {
  const { isMobile } = useScreenSize();
  return (
    <div
      onDragOver={onDragOver}
      onDrop={onDrop}
      onClick={onClick}
      className={cn(
        "border-b border-r relative cursor-pointer transition-colors group",
        isMobile ? "min-h-[80px] p-2" : "min-h-[110px] p-3",
        isCurrentMonth
          ? "bg-card hover:bg-accent"
          : "bg-muted hover:bg-muted/80",
        "border-border"
      )}
    >
      {children}
    </div>
  );
};

export const MonthView: React.FC<MonthViewProps> = ({
  selectedDate,
  events,
  selectedEvent,
  setSelectedEvent,
  setSelectedDate,
  openAddEventForm,
  canEditData,
  handleEventClick,
  onEventDrop
}) => {
  const { isMobile } = useScreenSize();
  const draggedEvent = useRef<CalendarEvent | null>(null);

  const handleDragStart = (event: CalendarEvent, e: React.DragEvent) => {
    if (!canEditData) return;

    draggedEvent.current = event;

    // Create a compact drag image for month view
    const dragImg = document.createElement('div');
    dragImg.style.position = 'absolute';
    dragImg.style.top = '-1000px';
    dragImg.style.left = '0px';
    dragImg.style.width = '65px';
    dragImg.style.height = '33px';
    dragImg.style.background = '#1e293b';  // Dark background
    dragImg.style.border = '1px solid #475569';
    dragImg.style.borderRadius = '4px';
    dragImg.style.padding = '6px 4px';
    dragImg.style.boxShadow = '0 2px 4px -1px rgb(0 0 0 / 0.15)';
    dragImg.style.fontSize = '9px';
    dragImg.style.fontWeight = '500';
    dragImg.style.color = 'white';
    dragImg.style.display = 'flex';
    dragImg.style.flexDirection = 'column';
    dragImg.style.justifyContent = 'center';
    dragImg.style.alignItems = 'center';
    dragImg.style.opacity = '0.9';
    dragImg.style.zIndex = '9999';
    dragImg.style.textAlign = 'center';

    dragImg.innerHTML = `
      <div style="font-weight: 600; color: white; line-height: 1.1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 62px; margin-bottom: 2px;">
        ${event.title}
      </div>
      <div style="font-size: 7px; color: #cbd5e1; line-height: 1;">
        ${format(new Date(event.start), 'h:mm a')}
      </div>
    `;

    document.body.appendChild(dragImg);
    e.dataTransfer.setDragImage(dragImg, 40, 25);
    setTimeout(() => {
      if (document.body.contains(dragImg)) {
        document.body.removeChild(dragImg);
      }
    }, 100);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!canEditData) return;
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (date: Date, e: React.DragEvent) => {
    e.preventDefault();
    if (!canEditData || !draggedEvent.current) return;

    const event = draggedEvent.current;
    const originalDate = new Date(event.start);

    // Calculate minutes based on drop position within the cell
    const cellHeight = (e.target as HTMLElement).offsetHeight;
    const rect = (e.target as HTMLElement).getBoundingClientRect();
    const relativeY = e.clientY - rect.top;
    const hourPercentage = relativeY / cellHeight;
    const hour = Math.floor(hourPercentage * 24);
    const minutes = Math.floor((hourPercentage * 24 - hour) * 60);

    const newDate = new Date(date);
    newDate.setHours(hour, minutes, 0, 0);

    if (newDate.getTime() === originalDate.getTime()) {
        draggedEvent.current = null;
        return;
    }

    onEventDrop?.(event, newDate);
    draggedEvent.current = null;
  };

  const monthStart = startOfMonth(selectedDate);
  const monthEnd = endOfMonth(selectedDate);
  const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });
  const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });

  const days = [];
  let day = startDay;
  while (day <= endDay) {
    days.push(day);
    day = addDays(day, 1);
  }

  const weeks = [];
  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7));
  }

  const monthEvents = events.filter(event => {
    const eventStart = new Date(event.start);
    return eventStart >= startDay && eventStart <= endDay;
  });

  if (monthEvents.length === 0) {
    return (
      <div className="flex flex-col h-full bg-background">
        {/* Compact Month Header */}
        <div className="grid grid-cols-7 border-b bg-secondary">
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
            <div key={dayName} className={cn(
              "text-center font-semibold text-foreground",
              isMobile ? "py-3 text-xs" : "py-4 text-sm"
            )}>
              {isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)}
            </div>
          ))}
        </div>

        {/* Enhanced Empty State */}
        <div className="flex-1 flex items-center justify-center bg-gradient-to-br from-secondary to-accent">
          <div className="text-center max-w-md mx-auto px-6">
            <div className="w-16 h-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-foreground mb-2">
              No events this month
            </h3>
            <p className="text-muted-foreground mb-6">
              {format(selectedDate, 'MMMM yyyy')} is completely free. Start planning your month!
            </p>
            {canEditData && (
              <Button
                onClick={() => openAddEventForm(selectedDate)}
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-6 py-2.5 rounded-lg shadow-sm"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                Create Event
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "h-full bg-background",
      isMobile ? "flex flex-col" : "flex"
    )}>
      <div className="flex-1 flex flex-col min-h-0">
        {/* Compact Month Header */}
        <div className="grid grid-cols-7 border-b sticky top-0 z-10 bg-secondary">
          {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map(dayName => (
            <div key={dayName} className={cn(
              "text-center font-semibold text-foreground",
              isMobile ? "py-3 text-xs" : "py-4 text-sm"
            )}>
              {isMobile ? dayName.substring(0, 3) : dayName.substring(0, 3)}
            </div>
          ))}
        </div>

        <div className="flex-1 grid grid-cols-7 auto-rows-fr border-gray-100 border-b">
          {weeks.map((week, weekIndex) => (
            week.map((day, dayIndex) => {
              const dayEvents = events.filter(event => isSameDay(new Date(event.start), day));
              const isCurrentMonth = day.getMonth() === selectedDate.getMonth();
              const isCurrentDay = isToday(day);

              return (
                <DayCell
                  key={`${weekIndex}-${dayIndex}`}
                  date={day}
                  isCurrentMonth={isCurrentMonth}
                  onClick={() => setSelectedDate(day)}
                  onDragOver={handleDragOver}
                  onDrop={(e) => handleDrop(day, e)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className={cn(
                      "inline-flex items-center justify-center rounded-full font-semibold",
                      isMobile ? "text-sm w-6 h-6" : "text-base w-7 h-7",
                      isCurrentDay
                        ? "bg-primary text-primary-foreground shadow-sm"
                        : isCurrentMonth
                          ? "text-foreground hover:bg-accent"
                          : "text-muted-foreground"
                    )}>
                      {format(day, 'd')}
                    </span>

                    {canEditData && isCurrentMonth && !isMobile && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-accent"
                        onClick={(e) => {
                          e.stopPropagation();
                          openAddEventForm(day);
                        }}
                      >
                        <PlusIcon className="h-3 w-3 text-muted-foreground" />
                      </Button>
                    )}
                  </div>

                  <div className={cn(
                    "space-y-1",
                    isMobile && "space-y-0.5"
                  )}>
                    {dayEvents.slice(0, isMobile ? 2 : 4).map(event => (
                      <CalendarEventItem
                        key={event.id}
                        event={event}
                        selectedEvent={selectedEvent}
                        canEditData={canEditData}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedEvent(event.id);
                          handleEventClick(event);
                        }}
                        onDragStart={handleDragStart}
                      />
                    ))}

                    {dayEvents.length > (isMobile ? 2 : 4) && (
                      <button
                        className={cn(
                          "text-primary hover:text-primary/80 font-medium pl-3 py-1 rounded-md hover:bg-accent transition-colors",
                          isMobile ? "text-[10px]" : "text-xs"
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedDate(day);
                        }}
                      >
                        + {dayEvents.length - (isMobile ? 2 : 4)} more
                      </button>
                    )}
                  </div>
                </DayCell>
              );
            })
          ))}
        </div>
      </div>

      {/* Enhanced Day Detail Sidebar */}
      {(!isMobile || (isMobile && selectedEvent)) && (
        <div className={cn(
          "border-l border-gray-200 flex flex-col bg-gray-50",
          isMobile ? "w-full border-t" : "w-96"
        )}>
          <div className={cn(
            "border-b border-gray-200 bg-white",
            isMobile ? "p-4" : "p-6"
          )}>
            <h2 className={cn(
              "font-bold text-gray-900 mb-1",
              isMobile ? "text-lg" : "text-xl"
            )}>
              {format(selectedDate, isMobile ? 'MMM d, yyyy' : 'MMMM d, yyyy')}
            </h2>
            <p className="text-sm text-gray-600">
              {events.filter(event => isSameDay(new Date(event.start), selectedDate)).length} events scheduled
            </p>
          </div>

          <ScrollArea className={cn(
            "flex-1",
            isMobile ? "p-4" : "p-6"
          )}>
            <div className="space-y-3">
              {events
                .filter(event => isSameDay(new Date(event.start), selectedDate))
                .sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime())
                .map(event => (
                  <Card
                    key={event.id}
                    className={cn(
                      "cursor-pointer transition-all duration-200 border-0 shadow-sm",
                      isMobile ? "p-3" : "p-4",
                      selectedEvent === event.id
                        ? "bg-primary text-primary-foreground shadow-lg ring-2 ring-primary/20"
                        : "bg-primary/5 hover:shadow-md hover:bg-primary/10"
                    )}
                    onClick={() => {
                      setSelectedEvent(event.id);
                      handleEventClick(event);
                    }}
                  >
                    <div className={cn(
                      "font-semibold mb-2",
                      isMobile ? "text-sm" : "text-base",
                      selectedEvent === event.id ? "text-primary-foreground" : "text-foreground"
                    )}>
                      {event.title}
                    </div>
                    <div className={cn(
                      "flex items-center space-x-2",
                      isMobile ? "text-xs" : "text-sm",
                      selectedEvent === event.id ? "text-primary-foreground/80" : "text-muted-foreground"
                    )}>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>
                        {format(new Date(event.start), 'h:mm a')} - {format(new Date(event.end), 'h:mm a')}
                      </span>
                    </div>
                  </Card>
                ))}
            </div>

            {events.filter(event => isSameDay(new Date(event.start), selectedDate)).length === 0 && (
              <div className="text-center py-8">
                <div className="w-12 h-12 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 002 2z" />
                  </svg>
                </div>
                <p className={cn(
                  "text-muted-foreground mb-4",
                  isMobile ? "text-sm" : "text-base"
                )}>
                  No events on this day
                </p>
                {canEditData && (
                  <Button
                    onClick={() => openAddEventForm(selectedDate)}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium px-4 py-2 rounded-lg shadow-sm"
                  >
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                    </svg>
                    Add Event
                  </Button>
                )}
              </div>
            )}
          </ScrollArea>
        </div>
      )}
    </div>
  );
};
